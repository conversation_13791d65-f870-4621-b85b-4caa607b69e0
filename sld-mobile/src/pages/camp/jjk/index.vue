<template>
  <view class="camp-page" :style="mix_diyStyle">
    <!-- 页面背景 -->
    <view class="page-background">
      <image
        class="bg-image"
        src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_bg.png"
        mode="aspectFill"
      />
    </view>

    <!-- 粒子效果背景 -->
    <canvas
      id="particleCanvas"
      canvas-id="particleCanvas"
      class="particle-canvas"
    ></canvas>

    <view class="container">
      <!-- 卡片区域 -->
      <view class="cards-container">
        <!-- 左侧卡片 -->
        <view class="side-card left-card">
          <image
            class="side-card-image"
            src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_side_bg.png"
            mode="aspectFit"
          />
        </view>

        <!-- 中间主卡片 -->
        <view class="main-card" :class="{ 'card-flip': isCardDrawn || isFlipping }" @click="handleCardClick">
          <!-- 卡片背面（默认显示） -->
          <view class="card-back">
            <image
              class="main-card-image"
              :src="cardBackImage"
              mode="aspectFit"
            />
          </view>
          <!-- 卡片正面（翻转后显示） -->
          <view class="card-front">
            <image
              class="main-card-image"
              :src="drawnCardImage"
              mode="aspectFit"
              v-if="drawnCardImage"
            />
          </view>
        </view>

        <!-- 右侧卡片 -->
        <view class="side-card right-card">
          <image
            class="side-card-image"
            src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_side_bg.png"
            mode="aspectFit"
          />
        </view>
      </view>

      <!-- 抽取按钮 -->
      <view class="draw-button-container">
        <image
          class="draw-button"
          src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_button.png"
          mode="widthFix"
          @click="drawCard"
          :class="{ 'disabled': isDrawing }"
        />
        <!-- <view class="button-text" v-if="isDrawing">抽取中...</view> -->
      </view>

      <!-- 分享按钮 - 只在抽取成功后显示 -->
      <view class="share-button-container" v-if="isCardDrawn && drawnCardImage">
        <image
          class="share-button-image"
          src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_share_button.png"
          @click="generatePoster"
          :class="{ 'disabled': isGeneratingPoster }"
          mode="aspectFit"
        />
      </view>

      <!-- 调试信息 -->
      <view style="position: fixed; top: 100rpx; left: 20rpx; background: rgba(0,0,0,0.8); color: white; padding: 10rpx; font-size: 24rpx; z-index: 999;">
        isCardDrawn: {{ isCardDrawn }}<br>
        drawnCardImage: {{ !!drawnCardImage }}<br>
        currentRid: {{ currentRid }}
      </view>

      <!-- 底部Banner区域 -->
      <view class="banner-container"></view>
    </view>

    <!-- 登录弹窗 -->
    <loginPop ref="loginPop"></loginPop>

    <!-- 海报分享弹窗 -->
    <view class="poster-modal" v-if="showPosterModal" @click="closePosterModal">
      <view class="poster-content" @click.stop>
        <view class="poster-header">
          <text class="poster-title">分享海报</text>
          <text class="close-btn" @click="closePosterModal">×</text>
        </view>
        <view class="poster-canvas-container">
          <canvas
            canvas-id="posterCanvas"
            class="poster-canvas"
            :style="{ width: posterCanvasWidth + 'px', height: posterCanvasHeight + 'px' }"
          ></canvas>
        </view>
        <view class="poster-actions">
          <view class="action-btn save-btn" @click="savePoster">保存到相册</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import loginPop from '@/components/loginPop/loginPop.vue'

export default {
  components: {
    loginPop
  },
  data() {
    return {
      // 卡片背面固定图片
      cardBackImage: 'http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_bg.png',
      // 抽取到的卡片图片（正面）
      drawnCardImage: null,
      isDrawing: false,
      isFlipping: false,
      // 卡片是否已经被抽取（决定显示正面还是背面）
      isCardDrawn: false,
      currentRid: null,
      groupKey: 'NzfYPDDe', // 彩虹卡资源组 - 用于CMS接口的表单组key
      isGeneratingPoster: false,
      showPosterModal: false,
      posterCanvasWidth: 375,
      posterCanvasHeight: 667,
      posterImagePath: '',
      resourceData: null, // 存储抽到的资源数据
      // Canvas粒子效果数据
      canvas: null,
      ctx: null,
      particleCanvasWidth: 375,
      particleCanvasHeight: 667,
      stars: [],
      starCount: 0,
      maxStars: 100,
      hue: 45, // 金色色调
      animationId: null
    }
  },
  computed: {
    ...mapState(['hasLogin'])
  },
  onLoad(options) {
    setTimeout(() => {
      uni.setNavigationBarTitle({
        title: '潜动力金句卡'
      })
    }, 0)

    // 检查是否有rid参数
    if (options.rid) {
      this.currentRid = options.rid
      this.loadCardByRid(options.rid)
    }
    // 背面始终显示固定图片，不需要额外设置

    // 初始化粒子效果
    this.initParticles()
  },
  onShow() {
    // #ifdef H5
    // H5端检查URL参数中的rid
    if (this.$Route && this.$Route.query && this.$Route.query.rid) {
      const rid = this.$Route.query.rid
      if (rid !== this.currentRid) {
        this.currentRid = rid
        this.loadCardByRid(rid)
      }
    }
    // #endif

    this.checkLogin()
  },
  onUnload() {
    // 清理动画
    this.stopAnimation()
  },
  methods: {
    // 检查登录状态
    checkLogin() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
      }
    },

    // 处理卡片点击 - 如果有预加载的图片，可以翻转查看
    handleCardClick() {
      if (this.isDrawing) return // 抽取中不允许点击

      // 如果有预加载的图片，允许翻转查看
      if (this.drawnCardImage && this.currentRid) {
        this.isCardDrawn = !this.isCardDrawn
      }
    },

    // 初始化粒子效果
    initParticles() {
      console.log('开始初始化Canvas粒子效果')

      // 延迟初始化，确保DOM已渲染
      this.$nextTick(() => {
        setTimeout(() => {
          this.initCanvas()
        }, 1000)
      })
    },

    // 初始化Canvas
    initCanvas() {
      console.log('initCanvas 开始执行')
      try {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync()
        this.particleCanvasWidth = systemInfo.windowWidth
        this.particleCanvasHeight = systemInfo.windowHeight

        console.log('屏幕尺寸:', this.particleCanvasWidth, this.particleCanvasHeight)

        // 创建Canvas上下文
        this.ctx = uni.createCanvasContext('particleCanvas', this)
        console.log('Canvas上下文对象:', this.ctx)

        if (this.ctx) {
          console.log('✅ Canvas上下文创建成功')

          // 先绘制一个测试圆形
          this.drawTestCircle()

          // 创建星星
          this.createStars()

          // 开始动画循环
          this.startAnimation()
        } else {
          console.error('❌ Canvas上下文创建失败')
        }
      } catch (error) {
        console.error('❌ Canvas初始化失败:', error)
      }
    },

    // 绘制测试圆形
    drawTestCircle() {
      console.log('绘制测试圆形')
      this.ctx.setFillStyle('#FF0000')
      this.ctx.beginPath()
      this.ctx.arc(100, 100, 30, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.draw()
      console.log('测试圆形绘制完成')
    },

    // 创建星星
    createStars() {
      this.stars = []
      this.starCount = 0

      for (let i = 0; i < this.maxStars; i++) {
        this.createStar()
      }

      console.log('创建了', this.stars.length, '个星星')
    },

    // 创建单个星星
    createStar() {
      const star = {
        orbitRadius: this.random(this.maxOrbit(this.particleCanvasWidth, this.particleCanvasHeight)),
        radius: this.random(1, 2), // 1-2像素
        orbitX: this.particleCanvasWidth / 2,
        orbitY: this.particleCanvasHeight / 2,
        timePassed: this.random(0, this.maxStars),
        speed: 0,
        alpha: this.random(2, 10) / 10
      }

      star.speed = this.random(star.orbitRadius) / 50000

      this.starCount++
      this.stars[this.starCount] = star
    },

    // 开始动画循环
    startAnimation() {
      console.log('开始动画循环')

      // 使用setInterval代替requestAnimationFrame，更稳定
      this.animationId = setInterval(() => {
        if (!this.ctx) return

        try {
          // 清除画布
          this.ctx.clearRect(0, 0, this.particleCanvasWidth, this.particleCanvasHeight)

          // 绘制背景
          this.ctx.setFillStyle('rgba(0, 0, 20, 0.1)')
          this.ctx.fillRect(0, 0, this.particleCanvasWidth, this.particleCanvasHeight)

          // 绘制星星
          for (let i = 1; i < this.stars.length; i++) {
            if (this.stars[i]) {
              this.drawStar(this.stars[i])
            }
          }

          this.ctx.draw()
        } catch (error) {
          console.error('动画绘制错误:', error)
        }
      }, 16) // 约60FPS
    },

    // 绘制星星
    drawStar(star) {
      const x = Math.sin(star.timePassed) * star.orbitRadius + star.orbitX
      const y = Math.cos(star.timePassed) * star.orbitRadius + star.orbitY
      const twinkle = this.random(30)

      // 闪烁效果
      if (twinkle === 1 && star.alpha > 0.2) {
        star.alpha -= 0.02
      } else if (twinkle === 2 && star.alpha < 0.8) {
        star.alpha += 0.02
      }

      // 计算当前透明度
      const alpha = Math.max(0.2, Math.min(1, star.alpha))

      // 绘制星星（简化版本）
      this.ctx.setFillStyle(`rgba(255, 215, 0, ${alpha})`)
      this.ctx.beginPath()
      this.ctx.arc(x, y, star.radius, 0, Math.PI * 2)
      this.ctx.fill()

      // 中心亮点（只有2像素的星星才有亮点）
      if (star.radius >= 2) {
        this.ctx.setFillStyle(`rgba(255, 255, 255, ${alpha * 0.8})`)
        this.ctx.beginPath()
        this.ctx.arc(x, y, star.radius * 0.4, 0, Math.PI * 2)
        this.ctx.fill()
      }

      star.timePassed += star.speed
    },

    // 工具函数：随机数
    random(min, max) {
      if (arguments.length < 2) {
        max = min
        min = 0
      }

      if (min > max) {
        const hold = max
        max = min
        min = hold
      }

      return Math.floor(Math.random() * (max - min + 1)) + min
    },

    // 工具函数：最大轨道半径
    maxOrbit(x, y) {
      const max = Math.max(x, y)
      const diameter = Math.round(Math.sqrt(max * max + max * max))
      return diameter / 2
    },

    // 创建爆发粒子效果
    createBurstParticles() {
      if (!this.ctx) return

      console.log('创建爆发粒子效果')

      // 临时增加更多亮星
      const burstCount = 30
      const centerX = this.particleCanvasWidth / 2
      const centerY = this.particleCanvasHeight * 0.45 // 卡片位置

      for (let i = 0; i < burstCount; i++) {
        const angle = (360 / burstCount) * i
        const distance = this.random(50, 150)

        const burstStar = {
          orbitRadius: distance,
          radius: this.random(1, 2), // 爆发粒子也是1-2像素
          orbitX: centerX,
          orbitY: centerY,
          timePassed: angle * Math.PI / 180,
          speed: this.random(100, 300) / 100000,
          alpha: 1,
          isBurst: true,
          life: 60 // 60帧生命周期
        }

        this.stars.push(burstStar)
      }

      // 1秒后清理爆发粒子
      setTimeout(() => {
        this.stars = this.stars.filter(star => !star.isBurst)
        console.log('清理爆发粒子，剩余星星数量:', this.stars.length)
      }, 1000)
    },

    // 停止动画
    stopAnimation() {
      if (this.animationId) {
        clearInterval(this.animationId)
        this.animationId = null
      }
    },

    // 根据rid加载对应卡片
    loadCardByRid(rid) {
      if (!rid) return

      // 根据技术方案，rid格式应该是 groupKey + id，比如 NzfYPDDe01
      // 这里我们先简单处理，直接根据rid构造图片URL
      // 实际项目中应该调用接口获取资源信息

      // 临时处理：如果rid是数字，则使用对应的图片
      if (/^\d+$/.test(rid)) {
        this.drawnCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${rid}.webp`
        this.currentRid = rid
        // 通过URL参数加载的卡片，显示为已抽取状态，这样分享按钮会显示
        this.isCardDrawn = true
      } else {
        // 调用新的CMS接口获取具体资源信息
        this.$request({
          url: '/v3/promotion/front/cms/get',
          method: 'GET',
          data: {
            groupKey: this.groupKey,
            rid: rid
          }
        }).then(res => {
          if (res.state === 200 && res.data) {
            this.resourceData = res.data
            // 根据新接口的数据结构调整字段访问
            const imageUrl = res.data.imageUrl || res.data.image || res.data.url

            if (imageUrl) {
              this.drawnCardImage = imageUrl
              this.currentRid = rid
              // 通过URL参数加载的卡片，显示为已抽取状态，这样分享按钮会显示
              this.isCardDrawn = true
              console.log('成功加载卡片资源:', { rid, imageUrl })
            } else {
              console.warn('CMS接口返回的数据中没有找到图片URL:', res.data)
              this.$api.msg('卡片资源加载失败')
              this.drawnCardImage = null
              this.isCardDrawn = false
            }
          } else {
            console.warn('CMS接口返回状态异常:', res)
            this.$api.msg(res.msg || '获取资源失败')
            this.drawnCardImage = null
            this.isCardDrawn = false
          }
        }).catch(err => {
          console.error('CMS接口调用失败:', err)
          this.$api.msg('网络错误，请稍后重试')
          this.drawnCardImage = null
          this.isCardDrawn = false
        })
      }
    },
    
    // 生成海报
    generatePoster() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
        return
      }
      
      if (this.isGeneratingPoster) {
        return
      }
      
      this.isGeneratingPoster = true
      
      uni.showLoading({
        title: '生成海报中...'
      })
      
      // 模拟生成海报
      setTimeout(() => {
        this.drawPosterCanvas()
      }, 500)
    },
    
    // 绘制海报画布
    drawPosterCanvas() {
      const ctx = uni.createCanvasContext('posterCanvas', this)
      
      // 设置背景色
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, this.posterCanvasWidth, this.posterCanvasHeight)

      // 绘制标题
      ctx.fillStyle = '#333333'
      ctx.font = 'bold 24px sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('金句卡分享', this.posterCanvasWidth / 2, 50)
      
      // 绘制卡片图片
      if (this.drawnCardImage && this.currentRid) {
        uni.getImageInfo({
          src: this.drawnCardImage,
          success: (res) => {
            const imgWidth = 200
            const imgHeight = 280
            const imgX = (this.posterCanvasWidth - imgWidth) / 2
            const imgY = 80

            ctx.drawImage(res.path, imgX, imgY, imgWidth, imgHeight)

            // 绘制卡片编号
            ctx.fillStyle = '#666666'
            ctx.font = '16px sans-serif'
            ctx.fillText(`卡片编号: ${this.currentRid}`, this.posterCanvasWidth / 2, imgY + imgHeight + 30)

            // 绘制底部文字
            ctx.fillStyle = '#999999'
            ctx.font = '14px sans-serif'
            ctx.fillText('扫码体验更多精彩内容', this.posterCanvasWidth / 2, this.posterCanvasHeight - 50)
            
            ctx.draw(false, () => {
              this.isGeneratingPoster = false
              this.showPosterModal = true
              uni.hideLoading()
            })
          },
          fail: () => {
            this.isGeneratingPoster = false
            uni.hideLoading()
            this.$api.msg('生成海报失败')
          }
        })
      } else {
        this.isGeneratingPoster = false
        uni.hideLoading()
        this.$api.msg('请先抽取卡片')
      }
    },
    
    // 保存海报
    savePoster() {
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              this.$api.msg('海报已保存到相册')
              this.closePosterModal()
            },
            fail: () => {
              this.$api.msg('保存失败，请检查相册权限')
            }
          })
        },
        fail: () => {
          this.$api.msg('生成图片失败')
        }
      })
    },
    
    // 关闭海报弹窗
    closePosterModal() {
      this.showPosterModal = false
    },
    
    // 抽卡函数
    drawCard() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
        return
      }

      if (this.isDrawing) {
        return
      }

      this.isDrawing = true
      // 重置卡片状态，确保从背面开始
      this.isCardDrawn = false
      this.isFlipping = false

      // 触发爆发粒子效果
      this.createBurstParticles()

      // 调用新的CMS接口进行抽卡
      // 使用 /v3/promotion/front/cms/get 接口替代原来的 front/camp/resource
      // 该接口支持通过 groupKey 随机获取表单数据
      this.$request({
        url: '/v3/promotion/front/cms/get',
        method: 'GET',
        data: {
          groupKey: this.groupKey
          // 不传rid参数，让服务端随机返回该组下的表单数据
        }
      }).then(res => {
        if (res.state === 200 && res.data) {
          const resourceData = res.data
          // 根据新接口的数据结构获取rid，可能在不同字段中
          const rid = resourceData.rid || resourceData.id || resourceData.formKey
          // 根据新接口的数据结构调整字段访问
          const imageUrl = resourceData.imageUrl || resourceData.image || resourceData.url

          if (rid && imageUrl) {
            // 更新rid到URL参数
            this.updateUrlWithRid(rid)

            // 模拟抽卡动画
            setTimeout(() => {
              // 设置抽取到的图片数据
              this.resourceData = resourceData
              this.drawnCardImage = imageUrl
              this.currentRid = rid

              // 开始翻转动画
              this.isFlipping = true

              // 翻转动画完成后显示正面
              setTimeout(() => {
                this.isCardDrawn = true
                this.isFlipping = false
                this.isDrawing = false
                this.$api.msg('恭喜获得金句卡！')
              }, 400) // 翻转动画时间
            }, 800)

            console.log('成功抽取卡片:', { rid, imageUrl })
          } else {
            console.warn('CMS接口返回的数据缺少必要字段:', { rid, imageUrl, data: resourceData })
            this.handleDrawCardFallback()
          }
        } else {
          console.log('CMS接口返回异常，使用模拟数据:', res)
          this.handleDrawCardFallback()
        }
      }).catch(err => {
        console.error('CMS抽卡接口调用失败，使用模拟数据:', err)
        this.handleDrawCardFallback()
      })
    },

    // 抽卡失败时的备用处理
    handleDrawCardFallback() {
      // 生成500-600之间的随机数作为演示
      const cardNumber = Math.floor(Math.random() * 100) + 500

      // 更新rid到URL参数
      this.updateUrlWithRid(cardNumber)

      // 模拟抽卡动画
      setTimeout(() => {
        // 设置抽取到的图片数据
        this.drawnCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${cardNumber}.webp`
        this.currentRid = cardNumber

        // 开始翻转动画
        this.isFlipping = true

        // 翻转动画完成后显示正面
        setTimeout(() => {
          this.isCardDrawn = true
          this.isFlipping = false
          this.isDrawing = false
          this.$api.msg('恭喜获得金句卡！')
        }, 400) // 翻转动画时间
      }, 800)
    },
    
    // 更新URL参数
    updateUrlWithRid(rid) {
      // #ifdef H5
      const currentUrl = new URL(window.location.href)
      currentUrl.searchParams.set('rid', rid)
      window.history.replaceState({}, '', currentUrl.toString())
      // #endif
      
      // #ifdef MP-WEIXIN
      // 小程序端可以考虑使用其他方式保存状态，比如本地存储
      uni.setStorageSync('current_card_rid', rid)
      // #endif
    },
    
    // 跳转商品链接
    goToProduct() {
      // #ifdef H5
      window.open('https://baidu.com', '_blank')
      // #endif
      // #ifndef H5
      uni.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent('https://baidu.com')}`
      })
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.camp-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .bg-image {
    width: 100%;
    height: 100%;
  }
}

.particle-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.container {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 120rpx 20rpx 200rpx;
}

.cards-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 800rpx;
  margin-bottom: 40rpx;
  perspective: 1000rpx;
}

.side-card {
  position: absolute;
  width: 320rpx;
  height: 500rpx;
  z-index: 1;
  animation: cardFloat 3s ease-in-out infinite;

  &.left-card {
    left: 20rpx;
    --rotate-y: -15deg;
    --rotate-z: -5deg;
    transform: rotateY(-15deg) rotateZ(-5deg);
    animation-delay: 0s;
  }

  &.right-card {
    right: 20rpx;
    --rotate-y: 15deg;
    --rotate-z: 5deg;
    transform: rotateY(15deg) rotateZ(5deg);
    animation-delay: 1.5s;
  }

  .side-card-image {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.4);
  }
}

.main-card {
  position: relative;
  z-index: 2;
  width: 440rpx;
  height: 776rpx;
  transition: transform 0.8s ease-in-out;
  transform-style: preserve-3d;

  &.card-flip {
    transform: rotateY(180deg);
  }

  .card-back,
  .card-front {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
  }

  .card-back {
    .main-card-image {
      width: 120%;
      height: 120%;
      margin: -10%;
      border-radius: 0;
      box-shadow: none;
      border: none;
    }
  }

  .card-front {
    .main-card-image {
      width: 100%;
      height: 100%;
      border-radius: 32rpx;
      box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.5);
      border: 2rpx solid rgba(255, 255, 255, 0.1);
    }
  }

  .card-back {
    transform: rotateY(0deg);
  }

  .card-front {
    transform: rotateY(180deg);
  }
}

.draw-button-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 12rpx;

  .draw-button {
    width: 360rpx;
    height: auto;
    transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));

    &:active {
      transform: scale(0.9);
      filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.4));
      transition: all 0.1s ease-out;
    }

    &.disabled {
      opacity: 0.7;
      transform: none;
      filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.2));
    }
  }

  .button-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 36rpx;
    font-weight: bold;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.8);
    pointer-events: none;
    z-index: 10;
  }
}

.share-button-container {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;

  .share-button-image {
    width: 360rpx; // 与抽取按钮相同宽度
    height: auto;
    transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));

    &:active {
      transform: scale(0.9);
      filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.4));
      transition: all 0.1s ease-out;
    }

    &.disabled {
      opacity: 0.7;
      transform: none;
      filter: drop-shadow(0 5rpx 10rpx rgba(0, 0, 0, 0.2));
    }
  }
}

.banner-container {
  position: fixed;
  bottom: 60rpx;
  left: 40rpx;
  right: 40rpx;
  height: 180rpx;
  background-image: url('https://sg-tduck-sh.oss-cn-shanghai.aliyuncs.com/jjk/imgs/banner-01.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 20rpx;
  z-index: 10;
}

/* 海报弹窗样式 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.poster-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.poster-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.poster-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.close-btn {
  font-size: 40rpx;
  color: white;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.poster-canvas-container {
  padding: 40rpx;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
}

.poster-canvas {
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.poster-actions {
  padding: 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
}

/* 动画效果 */
@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0rpx) rotateY(var(--rotate-y, 0deg)) rotateZ(var(--rotate-z, 0deg));
  }
  50% {
    transform: translateY(-20rpx) rotateY(var(--rotate-y, 0deg)) rotateZ(var(--rotate-z, 0deg));
  }
}



@keyframes buttonGlow {
  0%, 100% {
    filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));
  }
  50% {
    filter: drop-shadow(0 15rpx 30rpx rgba(255, 215, 0, 0.4));
  }
}

.draw-button {
  animation: buttonGlow 2s ease-in-out infinite;
}
</style>
